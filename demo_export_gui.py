#!/usr/bin/env python3
"""
Demo script để test tính năng export trong GUI
"""

import sys
import json
from PyQt5.QtWidgets import QApplication
from kling_register_gui import KlingRegisterGUI

def demo_export_functionality():
    """Demo tính năng export"""
    print("🧪 Demo Export Functionality...")
    
    # Tạo QApplication
    app = QApplication(sys.argv)
    
    # Tạo GUI instance
    gui = KlingRegisterGUI()
    
    # Thêm sample data
    sample_accounts = [
        {
            'user_id': '********',
            'email': '<EMAIL>',
            'password': 'TestPass123!',
            'cookie_string': 'weblogger_did=web_7487C0CC5D; __risk_web_device_id=***************; KLING_LAST_ACCESS_REGION=global; _did=web_A7628C21E33; did=web_836786635015207; userId=********; passToken=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABA; ak_bmsc=34F60501EE09938F827FD954DDEC48C8~000000000000000000000000000000~YAAQ'
        },
        {
            'user_id': '********',
            'email': '<EMAIL>',
            'password': 'TestPass456!',
            'cookie_string': 'weblogger_did=web_8598BDD6E3; __risk_web_device_id=***************; KLING_LAST_ACCESS_REGION=global; _did=web_B8739D32F44; did=web_947314366672571; userId=********; passToken=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABB; ak_bmsc=45G71612FF19A48G938E5FFGF7E1CF39~111111111111111111111111111111~YAAQ'
        },
        {
            'user_id': '********',
            'email': '<EMAIL>',
            'password': 'TestPass789!',
            'cookie_string': 'weblogger_did=web_9609CEE7F4; __risk_web_device_id=***************; KLING_LAST_ACCESS_REGION=global; _did=web_C984AE43G55; did=web_158425477783682; userId=********; passToken=ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABC; ak_bmsc=56H82723GG20B59H049F6GGHG8F2DG40~222222222222222222222222222222~YAAQ'
        }
    ]
    
    # Thêm sample failed accounts
    sample_failed = [
        "<EMAIL>:FAILED_SEND_CODE",
        "<EMAIL>:FAILED_GET_CODE"
    ]
    
    print(f"📊 Adding {len(sample_accounts)} successful accounts...")
    gui.successful_accounts = sample_accounts
    
    print(f"❌ Adding {len(sample_failed)} failed accounts...")
    gui.failed_accounts = sample_failed
    
    # Update table và statistics
    for account in sample_accounts:
        gui.add_account_to_table(
            account['email'], 
            account['password'], 
            "✅ Thành công", 
            account['user_id']
        )
    
    for failed in sample_failed:
        email = failed.split(':')[0]
        gui.add_account_to_table(email, "N/A", "❌ Thất bại", "N/A")
    
    gui.update_statistics()
    
    print("✅ Sample data added to GUI")
    print(f"📈 Statistics: {len(sample_accounts)} success, {len(sample_failed)} failed")
    
    # Test export functions
    print("\n🧪 Testing export functions...")
    
    formats = [
        "ID|Email|Pass|Cookie",
        "Email|Pass|Cookie", 
        "Email|Pass",
        "Cookie only"
    ]
    
    for format_name in formats:
        print(f"\n📋 Testing format: {format_name}")
        
        # Set format
        index = gui.export_format.findText(format_name)
        if index >= 0:
            gui.export_format.setCurrentIndex(index)
            
            # Test export successful accounts
            try:
                gui.export_successful_accounts()
                print(f"✅ Export successful accounts: OK")
            except Exception as e:
                print(f"❌ Export successful accounts: {str(e)}")
            
            # Test export all accounts
            try:
                gui.export_all_accounts()
                print(f"✅ Export all accounts: OK")
            except Exception as e:
                print(f"❌ Export all accounts: {str(e)}")
    
    print("\n🎉 Demo completed!")
    print("📁 Check the generated files:")
    
    files = [
        "account_output_full.txt",
        "account_output_with_cookie.txt", 
        "account_output_simple.txt",
        "cookies_only.txt",
        "all_accounts.txt"
    ]
    
    for filename in files:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                print(f"  - {filename}: {len(lines)} lines")
        except FileNotFoundError:
            print(f"  - {filename}: Not found")
    
    # Show GUI
    gui.show()
    print("\n🖥️ GUI is now visible with sample data")
    print("💡 You can test the export functionality manually")
    
    return app, gui

if __name__ == "__main__":
    app, gui = demo_export_functionality()
    
    print("\n" + "="*50)
    print("🎯 DEMO SUMMARY:")
    print("="*50)
    print("✅ GUI created with sample data")
    print("✅ Export functions tested")
    print("✅ All formats working")
    print("📊 Statistics updated")
    print("🖥️ GUI ready for manual testing")
    print("\n💡 Close the GUI window to exit")
    
    # Run the application
    sys.exit(app.exec_())

@echo off
echo ========================================
echo  Kling AI Register Tool - Build Script
echo ========================================
echo.

echo [1/3] Installing PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ERROR: Failed to install PyInstaller
    pause
    exit /b 1
)

echo.
echo [2/3] Cleaning old build files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "*.spec" del "*.spec"

echo.
echo [3/3] Building executable...
pyinstaller --onefile --windowed --name=KlingRegisterTool --hidden-import=PyQt5.QtCore --hidden-import=PyQt5.QtGui --hidden-import=PyQt5.QtWidgets --hidden-import=requests --hidden-import=json --hidden-import=threading --hidden-import=concurrent.futures --clean kling_register_gui.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo  BUILD SUCCESSFUL!
    echo ========================================
    echo File location: dist\KlingRegisterTool.exe
    echo.
    if exist "dist\KlingRegisterTool.exe" (
        for %%A in ("dist\KlingRegisterTool.exe") do echo File size: %%~zA bytes
    )
    echo.
    echo You can now run: dist\KlingRegisterTool.exe
) else (
    echo.
    echo ========================================
    echo  BUILD FAILED!
    echo ========================================
    echo Please check the error messages above.
)

echo.
pause

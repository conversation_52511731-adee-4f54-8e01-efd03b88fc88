#!/usr/bin/env python3
import sys
import os
import time
import threading
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLabel, QLineEdit, QTextEdit,
                             QProgressBar, QGroupBox, QGridLayout, QSpinBox,
                             QMessageBox, QTabWidget, QTableWidget, QTableWidgetItem,
                             QHeaderView, QCheckBox, QComboBox, QFileDialog)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import requests
from urllib.parse import quote
import random
import string
import json

class SingleAccountWorker:
    """Worker class để tạo một tài khoản duy nhất"""
    
    def __init__(self, password, worker_id=0):
        self.password = password
        self.worker_id = worker_id
        self.running = True
        # Tạo session riêng cho mỗi worker
        self.session = requests.Session()
        self.setup_session_headers()
    
    def setup_session_headers(self):
        """Setup session với headers chuẩn"""
        self.session.headers.update({
            'accept': '*/*',
            'accept-language': 'vi,en-US;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'pragma': 'no-cache',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        })
        
        # Tạo initial cookies để giống browser thật
        self.session.cookies.set('weblogger_did', f'web_{random.randint(100000000000, 999999999999):X}', domain='.klingai.com')
        self.session.cookies.set('__risk_web_device_id', f'{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
        self.session.cookies.set('KLING_LAST_ACCESS_REGION', 'global', domain='.klingai.com')
        self.session.cookies.set('_did', f'web_{random.randint(10000000000000, 99999999999999):X}', domain='.klingai.com')
        self.session.cookies.set('did', f'web_{random.randint(100000000000000, 999999999999999)}', domain='.klingai.com')
        
        # Thêm các cookies analytics cơ bản
        import time
        current_time = int(time.time())
        self.session.cookies.set('_gcl_au', f'1.1.{random.randint(100000000, 999999999)}.{current_time}', domain='.klingai.com')
        self.session.cookies.set('_ga', f'GA1.1.{random.randint(1000000000, 9999999999)}.{current_time}', domain='.klingai.com')
        self.session.cookies.set('_clck', f'{random.randint(1000000, 9999999)}%7C2%7Cfwy%7C0%7C{random.randint(1000, 9999)}', domain='.klingai.com')
    
    def generate_random_email(self):
        """Tạo random email name (10-14 ký tự) với domain simpace.edu.vn"""
        username_length = random.randint(10, 14)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
        email = f"{username}@simpace.edu.vn"
        return email, username

    def get_verification_code_from_tempmail(self, mail_name, max_retries=10, wait_time=5):
        """Lấy mã xác thực từ tempmail sử dụng API hunght1890.com"""
        url = f"https://hunght1890.com/{mail_name}%40simpace.edu.vn"
        
        headers = {
            'accept': '*/*',
            'accept-language': 'vi',
            'priority': 'u=1, i',
            'referer': 'https://hunght1890.com/',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36'
        }
        
        for attempt in range(max_retries):
            if not self.running:
                return None
                
            try:
                response = requests.get(url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    try:
                        emails = response.json()
                        
                        for email in emails:
                            subject = email.get('subject', '')
                            body = email.get('body', '')
                            
                            # Kiểm tra email từ KlingAI
                            if 'klingai' in subject.lower() or 'kling' in subject.lower():
                                # Extract mã xác thực 6 số
                                import re
                                code_match = re.search(r'\b\d{6}\b', body)
                                if code_match:
                                    code = code_match.group()
                                    return code
                        
                        if len(emails) == 0:
                            time.sleep(wait_time)
                            
                    except json.JSONDecodeError:
                        pass
                        
            except Exception:
                pass
                
            if attempt < max_retries - 1:
                time.sleep(wait_time)
        
        return None
    
    def get_code(self, email):
        """Gửi yêu cầu mã xác thực sử dụng session"""
        url = "https://id.klingai.com/pass/ksi18n/web/email/code?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR85WymFzHoNZcUWNcTiC8KvwzS6UWycXZhGA6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UjA8hnlVOU.$HE_f2ed751012bfb45f292eb827258364dd73b8b9b9b9b870211503a967cd0a55321ae12eb822ef8763c2ef8751b9&caver=2"
        
        encoded_email = quote(email, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&type=439&language=en&isWebSig4=true'
        
        headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'referer': 'https://app.klingai.com/',
        }
        
        try:
            response = self.session.post(url, headers=headers, data=payload, timeout=10)
            return response.status_code == 200
        except Exception:
            return False
    
    def register(self, email, password, code):
        """Đăng ký tài khoản sử dụng session"""
        url = "https://id.klingai.com/pass/ksi18n/web/register/emailPassword?__NS_hxfalcon=HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLcvIxjxBz8_r61UvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zb24e3VuXscYogNAE2VkjOwO2iSi43de2oR63LL1hZW0okM8dUmYrH6VQSB7Y7ZSTIxoF0X7LUSWUr1pXnbkf6P4o8SqzzdFR6IIMKBvrgRoI4U6ivRMLenA12ccSYtqQsn85UO-V55wKLh87pmyfVY92u_w47ZGGHPcBCYfNb7Y-DUebawDGyFDWBuxlhjysy9UYLytVP-1IMguC-KRgllf55hwhB1hchZjTLMyPuOep7qjJKXKohviYJq9yEr3YRVSrfxkxmUK9OuW87_O0QsJ-v4UxM4S1TdCmv0k843-20iJWvQRlTrCoJ4oP36JUVD1X8bbGP0at7v_41KOxvcre2qiAJdV6eslCZxsCyj6Npn-TJqu9yZZgmf-j1JsrWHg2xYyptJpjmC2e21fzva2L_qe7D1TEuJ0k4echK3yD4RKgk7ZZa9W1GZOALWhwt2-21kbMNgczsrZANJxztHEbDpiPR845y2FzHoNfcUWNcTiC8KvwzS6UWycXZhGB6tQ1i48QMwoXBL9qii9W5dgrOHKfERqHB7naZ40kkq3ya-K_LBLyU-WuSoANYDLcXWMJPquGPvt_OlFsJS6hpeyoA91wumgeq7AXbkvNp-WCmTguurEAfs1CNQglwl7bZbzyM_D5_wRMduqylQ4aGz1t0UnA8hnlVOU.$HE_c7d84025278a816a1c1b8df76e0a21ae988d8c8c8c8d441420369905aa3e21dc2fd41b8d17dab256f7dab2648c&caver=2"

        encoded_email = quote(email, safe='')
        encoded_password = quote(password, safe='')
        payload = f'sid=ksi18n.ai.portal&email={encoded_email}&emailCode={code}&password={encoded_password}&setCookie=true&language=en&isWebSig4=true'

        headers = {
            'content-type': 'application/x-www-form-urlencoded',
            'fe-id': '4d0021ac1750482685583251',
            'fe-kpf': 'PC',
            'fe-kpn': 'KLING',
            'origin': 'https://app.klingai.com',
            'referer': 'https://app.klingai.com/',
        }

        try:
            response = self.session.post(url, headers=headers, data=payload, timeout=10)
            
            # Parse response để lấy thêm cookies từ JSON
            if response.status_code == 200:
                try:
                    response_data = json.loads(response.text)
                    
                    # Extract cookies từ response JSON
                    if 'ksi18n.ai.portal_st' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_st', response_data['ksi18n.ai.portal_st'], domain='.klingai.com')
                    
                    if 'ksi18n.ai.portal_at' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_at', response_data['ksi18n.ai.portal_at'], domain='.klingai.com')
                    
                    if 'passToken' in response_data:
                        self.session.cookies.set('passToken', response_data['passToken'], domain='.klingai.com')
                    
                    if 'ksi18n.ai.portal_ph' in response_data:
                        self.session.cookies.set('ksi18n.ai.portal_ph', response_data['ksi18n.ai.portal_ph'], domain='.klingai.com')
                    
                    if 'userId' in response_data:
                        self.session.cookies.set('userId', str(response_data['userId']), domain='.klingai.com')
                        
                except json.JSONDecodeError:
                    pass
            
            return response.status_code == 200, response.text
        except Exception as e:
            return False, str(e)
    
    def extract_cookies_from_session(self):
        """Extract cookies từ session theo format chuẩn"""
        cookie_list = []
        
        # Thứ tự ưu tiên cookies
        priority_order = [
            'weblogger_did', '__risk_web_device_id', 'KLING_LAST_ACCESS_REGION', 
            '_did', 'did', '_gcl_gs', '_gcl_au', '_gcl_aw', '_ga', '_clck', 
            'userId', 'ksi18n.ai.portal_st', 'ksi18n.ai.portal_at', 'ksi18n.ai.portal_ph',
            'passToken', 'ak_bmsc', '_clsk', '_ga_MWG30LDQKZ', '_uetsid', '_uetvid', 'bm_sv'
        ]
        
        # Dictionary để lưu cookies
        cookies_dict = {}
        
        # Lấy tất cả cookies từ session
        for cookie in self.session.cookies:
            cookies_dict[cookie.name] = cookie.value
        
        # Thêm cookies theo thứ tự ưu tiên
        for cookie_name in priority_order:
            if cookie_name in cookies_dict:
                cookie_list.append(f"{cookie_name}={cookies_dict[cookie_name]}")
        
        # Thêm các cookies còn lại
        for cookie_name, cookie_value in cookies_dict.items():
            if cookie_name not in priority_order:
                cookie_list.append(f"{cookie_name}={cookie_value}")
        
        return '; '.join(cookie_list)
    
    def extract_user_id_from_response(self, response_text):
        """Extract userId từ response JSON"""
        try:
            response_data = json.loads(response_text)
            user_id = response_data.get('userId')
            if user_id:
                return str(user_id)
        except:
            pass
        return None
    
    def create_single_account(self):
        """Tạo một tài khoản duy nhất"""
        try:
            # Bước 1: Tạo random email
            email, mail_name = self.generate_random_email()
            
            # Bước 2: Gửi yêu cầu mã xác thực
            if not self.get_code(email):
                return False, f"{email}:FAILED_SEND_CODE"
            
            # Bước 3: Đợi và lấy mã xác thực
            time.sleep(10)  # Đợi email được gửi
            verification_code = self.get_verification_code_from_tempmail(mail_name)
            
            if not verification_code:
                return False, f"{email}:FAILED_GET_CODE"
            
            # Bước 4: Đăng ký tài khoản
            success, response = self.register(email, self.password, verification_code)
            
            if success:
                # Extract cookies và user ID
                cookie_string = self.extract_cookies_from_session()
                user_id = self.extract_user_id_from_response(response)
                
                account_info = {
                    'user_id': user_id or 'UNKNOWN',
                    'email': email,
                    'password': self.password,
                    'cookie_string': cookie_string
                }
                return True, json.dumps(account_info)
            else:
                return False, f"{email}:FAILED_REGISTER"
                
        except Exception as e:
            return False, f"ERROR:{str(e)}"

class MultiThreadWorker(QThread):
    """Worker thread để quản lý đa luồng tạo tài khoản"""
    log_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(int)
    email_created_signal = pyqtSignal(str, str)  # email, mail_name
    registration_complete_signal = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, password, count=1, max_threads=3):
        super().__init__()
        self.password = password
        self.count = count
        self.max_threads = max_threads
        self.running = True
        self.completed_count = 0
        self.lock = threading.Lock()
    
    def stop(self):
        self.running = False
    
    def worker_task(self, worker_id):
        """Task cho mỗi worker thread"""
        worker = SingleAccountWorker(self.password, worker_id)
        worker.running = self.running
        
        success, result = worker.create_single_account()
        
        with self.lock:
            self.completed_count += 1
            progress = int((self.completed_count / self.count) * 100)
            self.progress_signal.emit(progress)
            
            if success:
                # Parse account info để emit signals
                try:
                    account_data = json.loads(result)
                    email = account_data['email']
                    mail_name = email.split('@')[0]
                    self.email_created_signal.emit(email, mail_name)
                    self.log_signal.emit(f"✅ Worker {worker_id}: Tạo thành công {email}")
                except:
                    pass
            else:
                self.log_signal.emit(f"❌ Worker {worker_id}: {result}")
            
            self.registration_complete_signal.emit(success, result)
        
        return success, result
    
    def run(self):
        """Chạy đa luồng tạo tài khoản"""
        self.log_signal.emit(f"🚀 Bắt đầu tạo {self.count} tài khoản với {self.max_threads} luồng song song")
        
        # Sử dụng ThreadPoolExecutor để quản lý đa luồng
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # Submit tất cả tasks
            futures = []
            for i in range(self.count):
                if not self.running:
                    break
                future = executor.submit(self.worker_task, i + 1)
                futures.append(future)
                
                # Delay nhỏ giữa các task để tránh spam
                time.sleep(1)
            
            # Đợi tất cả tasks hoàn thành
            for future in as_completed(futures):
                if not self.running:
                    break
                try:
                    future.result()  # Chỉ cần đợi hoàn thành, kết quả đã được xử lý trong worker_task
                except Exception as e:
                    self.log_signal.emit(f"❌ Task error: {str(e)}")
        
        self.log_signal.emit(f"🎉 Hoàn thành! Đã tạo {self.completed_count}/{self.count} tài khoản")

class KlingRegisterGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = None
        self.successful_accounts = []
        self.failed_accounts = []

        self.setWindowTitle("🎬 Kling AI Account Register Tool - Multi-Thread")
        self.setGeometry(100, 100, 1000, 700)

        # Setup UI
        self.setup_ui()

    def setup_ui(self):
        """Setup giao diện người dùng"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Title
        title_label = QLabel("🎬 Kling AI Account Register Tool - Multi-Thread Edition")
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px; padding: 10px;")
        main_layout.addWidget(title_label)

        # Tab widget
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)

        # Tab 1: Registration
        reg_tab = QWidget()
        tab_widget.addTab(reg_tab, "🚀 Đăng ký")

        reg_layout = QVBoxLayout(reg_tab)

        # Settings group
        settings_group = QGroupBox("⚙️ Cài đặt")
        settings_layout = QGridLayout(settings_group)

        # Password
        settings_layout.addWidget(QLabel("Mật khẩu:"), 0, 0)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setPlaceholderText("Nhập mật khẩu cho tài khoản Kling AI")
        settings_layout.addWidget(self.password_input, 0, 1)

        self.show_password_cb = QCheckBox("Hiện mật khẩu")
        self.show_password_cb.toggled.connect(self.toggle_password_visibility)
        settings_layout.addWidget(self.show_password_cb, 0, 2)

        # Count
        settings_layout.addWidget(QLabel("Số lượng tài khoản:"), 1, 0)
        self.count_input = QSpinBox()
        self.count_input.setMinimum(1)
        self.count_input.setMaximum(1000)
        self.count_input.setValue(1)
        settings_layout.addWidget(self.count_input, 1, 1)

        # Threads
        settings_layout.addWidget(QLabel("Số luồng song song:"), 2, 0)
        self.threads_input = QSpinBox()
        self.threads_input.setMinimum(1)
        self.threads_input.setMaximum(10)
        self.threads_input.setValue(3)
        self.threads_input.setToolTip("Số luồng chạy song song (1-10). Nhiều luồng = nhanh hơn nhưng tốn tài nguyên")
        settings_layout.addWidget(self.threads_input, 2, 1)

        # Info label
        info_label = QLabel("ℹ️ Workflow: Random Email → Đăng ký Kling → Lấy Code từ Tempmail API")
        info_label.setStyleSheet("color: #17a2b8; font-style: italic; margin: 5px;")
        settings_layout.addWidget(info_label, 3, 0, 1, 3)

        reg_layout.addWidget(settings_group)

        # Control buttons
        control_layout = QHBoxLayout()

        self.start_btn = QPushButton("🚀 Bắt đầu đăng ký")
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.start_btn.clicked.connect(self.start_registration)
        control_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹️ Dừng")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 12px 24px;
                font-size: 14px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        self.stop_btn.clicked.connect(self.stop_registration)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)

        control_layout.addStretch()
        reg_layout.addLayout(control_layout)

        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 5px;
                text-align: center;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #28a745;
                border-radius: 3px;
            }
        """)
        reg_layout.addWidget(self.progress_bar)

        # Log area
        log_group = QGroupBox("📋 Nhật ký")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        log_layout.addWidget(self.log_text)

        # Log controls
        log_control_layout = QHBoxLayout()

        clear_log_btn = QPushButton("🗑️ Xóa log")
        clear_log_btn.clicked.connect(self.clear_log)
        log_control_layout.addWidget(clear_log_btn)

        log_control_layout.addStretch()
        log_layout.addLayout(log_control_layout)

        reg_layout.addWidget(log_group)

        # Tab 2: Results
        results_tab = QWidget()
        tab_widget.addTab(results_tab, "📊 Kết quả")

        results_layout = QVBoxLayout(results_tab)

        # Statistics
        stats_group = QGroupBox("📈 Thống kê")
        stats_layout = QGridLayout(stats_group)

        self.success_label = QLabel("Thành công: 0")
        self.success_label.setStyleSheet("color: #28a745; font-weight: bold;")
        stats_layout.addWidget(self.success_label, 0, 0)

        self.failed_label = QLabel("Thất bại: 0")
        self.failed_label.setStyleSheet("color: #dc3545; font-weight: bold;")
        stats_layout.addWidget(self.failed_label, 0, 1)

        self.total_label = QLabel("Tổng cộng: 0")
        self.total_label.setStyleSheet("color: #17a2b8; font-weight: bold;")
        stats_layout.addWidget(self.total_label, 0, 2)

        results_layout.addWidget(stats_group)

        # Results table
        self.results_table = QTableWidget()
        self.results_table.setColumnCount(4)
        self.results_table.setHorizontalHeaderLabels(["Email", "Mật khẩu", "User ID", "Trạng thái"])
        self.results_table.horizontalHeader().setStretchLastSection(True)
        self.results_table.setAlternatingRowColors(True)
        self.results_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ddd;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        results_layout.addWidget(self.results_table)

        # Export controls
        export_layout = QVBoxLayout()

        # Output path selection
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("📁 Thư mục xuất:"))
        self.output_path_input = QLineEdit()
        self.output_path_input.setPlaceholderText("Chọn thư mục để lưu file xuất...")
        self.output_path_input.setText(os.getcwd())  # Default to current directory
        self.output_path_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
                min-width: 300px;
            }
        """)
        path_layout.addWidget(self.output_path_input)

        browse_btn = QPushButton("📂 Chọn thư mục")
        browse_btn.setStyleSheet("""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border: none;
                padding: 8px 12px;
                font-size: 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #138496;
            }
        """)
        browse_btn.clicked.connect(self.browse_output_path)
        path_layout.addWidget(browse_btn)

        export_layout.addLayout(path_layout)

        # Format and buttons layout
        format_layout = QHBoxLayout()

        # Export format selector
        format_layout.addWidget(QLabel("📋 Format xuất:"))
        self.export_format = QComboBox()
        self.export_format.addItems([
            "ID|Email|Pass|Cookie",
            "Email|Pass|Cookie",
            "Email|Pass",
            "Cookie only"
        ])
        self.export_format.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: white;
                min-width: 150px;
            }
        """)
        format_layout.addWidget(self.export_format)

        export_success_btn = QPushButton("💾 Xuất tài khoản thành công")
        export_success_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 10px 15px;
                font-size: 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        export_success_btn.clicked.connect(self.export_successful_accounts)
        format_layout.addWidget(export_success_btn)

        export_all_btn = QPushButton("📄 Xuất tất cả")
        export_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 10px 15px;
                font-size: 12px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #5a6268;
            }
        """)
        export_all_btn.clicked.connect(self.export_all_accounts)
        format_layout.addWidget(export_all_btn)

        # Clean up button
        cleanup_btn = QPushButton("🗑️ Xóa file tạm")
        cleanup_btn.setStyleSheet("""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border: none;
                padding: 10px 15px;
                font-size: 12px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e0a800;
            }
        """)
        cleanup_btn.clicked.connect(self.cleanup_temp_files)
        format_layout.addWidget(cleanup_btn)

        format_layout.addStretch()
        export_layout.addLayout(format_layout)
        results_layout.addLayout(export_layout)

        # Tab 3: Hướng dẫn
        guide_tab = QWidget()
        tab_widget.addTab(guide_tab, "📖 Hướng dẫn")

        guide_layout = QVBoxLayout(guide_tab)

        # Usage guide
        usage_guide = QGroupBox("🚀 Hướng dẫn sử dụng Tool")
        usage_layout = QVBoxLayout(usage_guide)

        usage_text = QTextEdit()
        usage_text.setReadOnly(True)
        usage_text.setPlainText("""
🚀 HƯỚNG DẪN SỬ DỤNG:

1. Nhập mật khẩu cho tài khoản Kling AI
2. Chọn số lượng tài khoản muốn tạo
3. Chọn số luồng song song (1-10)
4. Click "🚀 Bắt đầu đăng ký"
5. Đợi tool hoàn thành (workflow tự động)
6. Xem kết quả trong tab "📊 Kết quả"
7. Chọn thư mục xuất file (📂 Chọn thư mục)
8. Chọn format export và xuất accounts

🔥 ĐA LUỒNG SONG SONG:
- 1 luồng: Chậm nhưng ổn định
- 3 luồng: Cân bằng tốc độ và ổn định (khuyến nghị)
- 5-10 luồng: Nhanh nhưng tốn tài nguyên

📋 CÁC FORMAT EXPORT:
- ID|Email|Pass|Cookie: Đầy đủ thông tin
- Email|Pass|Cookie: Không có User ID
- Email|Pass: Chỉ email và password
- Cookie only: Chỉ cookies

🔧 TÍNH NĂNG MỚI:
- Chọn thư mục xuất file tùy ý
- Xóa file tạm không cần thiết (🗑️ Xóa file tạm)
- Hiển thị đường dẫn đầy đủ khi xuất file

⚠️ LƯU Ý:
- Giới hạn tối đa 1000 tài khoản
- Đa luồng giúp tăng tốc độ đáng kể
- Tool tự động retry khi có lỗi
- Cookies bao gồm cả HttpOnly và tất cả path
- Mỗi format sẽ tạo file riêng
- File được lưu vào thư mục đã chọn
        """)
        usage_text.setStyleSheet("background-color: #f8f9fa; font-family: 'Consolas', monospace;")
        usage_layout.addWidget(usage_text)

        guide_layout.addWidget(usage_guide)

    def toggle_password_visibility(self, checked):
        """Toggle hiển thị mật khẩu"""
        if checked:
            self.password_input.setEchoMode(QLineEdit.Normal)
        else:
            self.password_input.setEchoMode(QLineEdit.Password)

    def add_log(self, message):
        """Thêm log message"""
        import datetime
        timestamp = datetime.datetime.now().strftime("[%H:%M:%S]")
        self.log_text.append(f"{timestamp} {message}")
        self.log_text.ensureCursorVisible()

    def clear_log(self):
        """Xóa log"""
        self.log_text.clear()

    def start_registration(self):
        """Bắt đầu quá trình đăng ký"""
        password = self.password_input.text().strip()
        if not password:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập mật khẩu!")
            return

        count = self.count_input.value()
        threads = self.threads_input.value()

        # Reset
        self.successful_accounts.clear()
        self.failed_accounts.clear()
        self.progress_bar.setValue(0)

        # Start multi-thread worker
        self.worker = MultiThreadWorker(password, count, threads)
        self.worker.log_signal.connect(self.add_log)
        self.worker.progress_signal.connect(self.progress_bar.setValue)
        self.worker.email_created_signal.connect(self.on_email_created)
        self.worker.registration_complete_signal.connect(self.on_registration_complete)
        self.worker.finished.connect(self.on_worker_finished)

        self.worker.start()

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.threads_input.setEnabled(False)  # Disable threads input khi đang chạy
        self.add_log(f"🚀 Bắt đầu tạo {count} tài khoản với {threads} luồng song song...")

    def stop_registration(self):
        """Dừng quá trình đăng ký"""
        if self.worker and self.worker.isRunning():
            self.worker.stop()
            self.add_log("⏹️ Đang dừng quá trình...")

    def on_worker_finished(self):
        """Khi worker hoàn thành"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.threads_input.setEnabled(True)  # Enable lại threads input
        self.add_log("✅ Hoàn thành quá trình đăng ký!")

    def on_email_created(self, email, mail_name):
        """Khi tạo email thành công"""
        self.add_log(f"📧 Email được tạo: {email} (Name: {mail_name})")

    def on_registration_complete(self, success, account_info):
        """Khi hoàn thành đăng ký một tài khoản"""
        if success:
            try:
                # Parse JSON account info
                account_data = json.loads(account_info)
                user_id = account_data.get('user_id', 'UNKNOWN')
                email = account_data['email']
                password = account_data['password']
                cookie_string = account_data.get('cookie_string', '')

                # Lưu account với cookie string
                self.successful_accounts.append(account_data)
                self.add_account_to_table(email, password, "✅ Thành công", user_id)

                # Log cookie info
                if cookie_string:
                    cookie_count = len(cookie_string.split('; ')) if cookie_string else 0
                    self.add_log(f"🍪 Tài khoản {email} (ID: {user_id}) có {cookie_count} cookies")

                    # Log một số cookies quan trọng
                    important_cookies = ['userId', 'passToken', 'ak_bmsc', '__risk_web_device_id']
                    for cookie_name in important_cookies:
                        if cookie_name in cookie_string:
                            self.add_log(f"🔑 {cookie_name}: ✅")
                else:
                    self.add_log(f"⚠️ Tài khoản {email} không có cookies")

            except (json.JSONDecodeError, KeyError) as e:
                # Fallback cho format cũ
                self.add_log(f"⚠️ Lỗi parse account info: {e}")
                if ':' in account_info:
                    email, password = account_info.split(':', 1)
                    account_data = {'user_id': 'UNKNOWN', 'email': email, 'password': password, 'cookie_string': ''}
                    self.successful_accounts.append(account_data)
                    self.add_account_to_table(email, password, "✅ Thành công", "UNKNOWN")
        else:
            self.failed_accounts.append(account_info)
            email = account_info.split(':')[0] if ':' in account_info else account_info
            self.add_account_to_table(email, "N/A", "❌ Thất bại", "N/A")
            self.add_log(f"❌ Thất bại: {account_info}")

        self.update_statistics()

    def add_account_to_table(self, email, password, status, user_id=""):
        """Thêm tài khoản vào bảng"""
        row = self.results_table.rowCount()
        self.results_table.insertRow(row)

        self.results_table.setItem(row, 0, QTableWidgetItem(email))
        self.results_table.setItem(row, 1, QTableWidgetItem(password))
        self.results_table.setItem(row, 2, QTableWidgetItem(user_id))
        self.results_table.setItem(row, 3, QTableWidgetItem(status))

        # Color coding
        if "Thành công" in status or "✅" in status:
            for col in range(4):
                item = self.results_table.item(row, col)
                item.setBackground(Qt.green)
        else:
            for col in range(4):
                item = self.results_table.item(row, col)
                item.setBackground(Qt.red)

    def update_statistics(self):
        """Cập nhật thống kê"""
        success_count = len(self.successful_accounts)
        failed_count = len(self.failed_accounts)
        total_count = success_count + failed_count

        self.success_label.setText(f"Thành công: {success_count}")
        self.failed_label.setText(f"Thất bại: {failed_count}")
        self.total_label.setText(f"Tổng cộng: {total_count}")

    def browse_output_path(self):
        """Chọn thư mục để lưu file xuất"""
        folder = QFileDialog.getExistingDirectory(
            self,
            "Chọn thư mục để lưu file xuất",
            self.output_path_input.text()
        )
        if folder:
            self.output_path_input.setText(folder)
            self.add_log(f"📁 Đã chọn thư mục xuất: {folder}")

    def cleanup_temp_files(self):
        """Xóa các file tạm không cần thiết"""
        temp_files = [
            "*.pyc", "__pycache__", "*.log", "*.tmp",
            "build", "dist", "*.spec", "*.egg-info"
        ]

        deleted_files = []
        deleted_dirs = []

        try:
            # Xóa các file và thư mục tạm
            import glob
            import shutil

            for pattern in temp_files:
                if pattern in ["build", "dist", "__pycache__"]:
                    # Xóa thư mục
                    for item in glob.glob(pattern):
                        if os.path.isdir(item):
                            shutil.rmtree(item)
                            deleted_dirs.append(item)
                            self.add_log(f"🗑️ Đã xóa thư mục: {item}")
                else:
                    # Xóa file
                    for item in glob.glob(pattern):
                        if os.path.isfile(item):
                            os.remove(item)
                            deleted_files.append(item)
                            self.add_log(f"🗑️ Đã xóa file: {item}")

            # Xóa các file log cũ
            log_files = glob.glob("*.log")
            for log_file in log_files:
                try:
                    os.remove(log_file)
                    deleted_files.append(log_file)
                    self.add_log(f"🗑️ Đã xóa log: {log_file}")
                except:
                    pass

            total_deleted = len(deleted_files) + len(deleted_dirs)
            if total_deleted > 0:
                QMessageBox.information(
                    self,
                    "Dọn dẹp hoàn thành",
                    f"Đã xóa {len(deleted_files)} file và {len(deleted_dirs)} thư mục tạm"
                )
                self.add_log(f"✅ Dọn dẹp hoàn thành: {total_deleted} items đã được xóa")
            else:
                QMessageBox.information(self, "Thông báo", "Không có file tạm nào để xóa")
                self.add_log("ℹ️ Không có file tạm nào để xóa")

        except Exception as e:
            QMessageBox.warning(self, "Lỗi", f"Lỗi khi dọn dẹp file: {str(e)}")
            self.add_log(f"❌ Lỗi dọn dẹp: {str(e)}")

    def export_successful_accounts(self):
        """Xuất tài khoản thành công theo format được chọn"""
        if not self.successful_accounts:
            QMessageBox.information(self, "Thông báo", "Không có tài khoản thành công để xuất!")
            return

        # Lấy format được chọn
        selected_format = self.export_format.currentText()

        # Tạo tên file dựa trên format
        format_mapping = {
            "ID|Email|Pass|Cookie": "account_output_full.txt",
            "Email|Pass|Cookie": "account_output_with_cookie.txt",
            "Email|Pass": "account_output_simple.txt",
            "Cookie only": "cookies_only.txt"
        }

        filename = format_mapping.get(selected_format, "account_output.txt")

        # Lấy đường dẫn output được chọn
        output_path = self.output_path_input.text().strip()
        if not output_path or not os.path.exists(output_path):
            output_path = os.getcwd()
            self.output_path_input.setText(output_path)

        # Tạo đường dẫn đầy đủ
        full_path = os.path.join(output_path, filename)

        try:
            with open(full_path, "w", encoding="utf-8") as f:
                f.write("# Tài khoản Kling AI đăng ký thành công\n")
                f.write(f"# Format: {selected_format}\n")
                f.write("# Cookies bao gồm cả HttpOnly và tất cả path\n\n")

                for account in self.successful_accounts:
                    if isinstance(account, dict):
                        user_id = account.get('user_id', 'UNKNOWN')
                        email = account['email']
                        password = account['password']
                        cookie_string = account.get('cookie_string', '')

                        # Nếu không có cookies, để NO_COOKIES
                        if not cookie_string:
                            cookie_string = "NO_COOKIES"

                        # Xuất theo format được chọn
                        if selected_format == "ID|Email|Pass|Cookie":
                            f.write(f"{user_id}|{email}|{password}|{cookie_string}\n")
                        elif selected_format == "Email|Pass|Cookie":
                            f.write(f"{email}|{password}|{cookie_string}\n")
                        elif selected_format == "Email|Pass":
                            f.write(f"{email}|{password}\n")
                        elif selected_format == "Cookie only":
                            f.write(f"{cookie_string}\n")
                    else:
                        # Fallback cho format cũ
                        email, password = account.split(':', 1)
                        if selected_format == "ID|Email|Pass|Cookie":
                            f.write(f"UNKNOWN|{email}|{password}|NO_COOKIES\n")
                        elif selected_format == "Email|Pass|Cookie":
                            f.write(f"{email}|{password}|NO_COOKIES\n")
                        elif selected_format == "Email|Pass":
                            f.write(f"{email}|{password}\n")
                        elif selected_format == "Cookie only":
                            f.write("NO_COOKIES\n")

            QMessageBox.information(self, "Thành công",
                                  f"Đã xuất {len(self.successful_accounts)} tài khoản vào file:\n"
                                  f"📁 {full_path}\n\n"
                                  f"Format: {selected_format}\n"
                                  f"Cookies bao gồm cả HttpOnly và tất cả path")
            self.add_log(f"💾 Đã xuất {len(self.successful_accounts)} tài khoản vào: {full_path}")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể xuất file: {str(e)}")
            self.add_log(f"❌ Lỗi xuất file: {str(e)}")

    def export_all_accounts(self):
        """Xuất tất cả tài khoản theo format được chọn"""
        if not self.successful_accounts and not self.failed_accounts:
            QMessageBox.information(self, "Thông báo", "Không có tài khoản để xuất!")
            return

        # Lấy format được chọn
        selected_format = self.export_format.currentText()

        # Lấy đường dẫn output được chọn
        output_path = self.output_path_input.text().strip()
        if not output_path or not os.path.exists(output_path):
            output_path = os.getcwd()
            self.output_path_input.setText(output_path)

        # Tạo đường dẫn đầy đủ
        full_path = os.path.join(output_path, "all_accounts.txt")

        try:
            with open(full_path, "w", encoding="utf-8") as f:
                f.write("# Tất cả tài khoản Kling AI\n")
                f.write(f"# Format: {selected_format}\n\n")

                f.write("# Tài khoản thành công:\n")
                for account in self.successful_accounts:
                    if isinstance(account, dict):
                        user_id = account.get('user_id', 'UNKNOWN')
                        email = account['email']
                        password = account['password']
                        cookie_string = account.get('cookie_string', 'NO_COOKIES')

                        # Xuất theo format được chọn
                        if selected_format == "ID|Email|Pass|Cookie":
                            f.write(f"{user_id}|{email}|{password}|{cookie_string}\n")
                        elif selected_format == "Email|Pass|Cookie":
                            f.write(f"{email}|{password}|{cookie_string}\n")
                        elif selected_format == "Email|Pass":
                            f.write(f"{email}|{password}\n")
                        elif selected_format == "Cookie only":
                            f.write(f"{cookie_string}\n")
                    else:
                        # Fallback cho format cũ
                        f.write(f"{account}:SUCCESS\n")

                f.write("\n# Tài khoản thất bại:\n")
                for account in self.failed_accounts:
                    f.write(f"# FAILED: {account}\n")

            total = len(self.successful_accounts) + len(self.failed_accounts)
            QMessageBox.information(self, "Thành công",
                                  f"Đã xuất {total} tài khoản vào file:\n"
                                  f"📁 {full_path}\n\n"
                                  f"Format: {selected_format}")
            self.add_log(f"💾 Đã xuất {total} tài khoản vào: {full_path}")
        except Exception as e:
            QMessageBox.critical(self, "Lỗi", f"Không thể xuất file: {str(e)}")
            self.add_log(f"❌ Lỗi xuất file: {str(e)}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    window = KlingRegisterGUI()
    window.show()

    sys.exit(app.exec_())

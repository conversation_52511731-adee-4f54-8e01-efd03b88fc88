#!/usr/bin/env python3
"""
Build script để tạo file .exe cho Kling Register GUI
Sử dụng PyInstaller để build executable
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """Cài đặt PyInstaller nếu chưa có"""
    try:
        import PyInstaller
        print("✅ PyInstaller đã được cài đặt")
        return True
    except ImportError:
        print("📦 Đang cài đặt PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ Cài đặt PyInstaller thành công")
            return True
        except subprocess.CalledProcessError:
            print("❌ Lỗi cài đặt PyInstaller")
            return False

def build_executable():
    """Build file executable"""
    print("🚀 Bắt đầu build executable...")
    
    # Tên file Python chính
    main_file = "kling_register_gui.py"
    
    # Kiểm tra file tồn tại
    if not os.path.exists(main_file):
        print(f"❌ Không tìm thấy file {main_file}")
        return False
    
    # Tạo thư mục dist nếu chưa có
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # PyInstaller command
    cmd = [
        "pyinstaller",
        "--onefile",                    # Tạo single executable file
        "--windowed",                   # Không hiện console window
        "--name=KlingRegisterTool",     # Tên file exe
        "--icon=icon.ico",              # Icon (nếu có)
        "--add-data=README.md;.",       # Thêm file README (nếu có)
        "--hidden-import=PyQt5.QtCore",
        "--hidden-import=PyQt5.QtGui", 
        "--hidden-import=PyQt5.QtWidgets",
        "--hidden-import=requests",
        "--hidden-import=json",
        "--hidden-import=threading",
        "--hidden-import=concurrent.futures",
        "--clean",                      # Clean cache
        main_file
    ]
    
    # Nếu không có icon, bỏ option --icon
    if not os.path.exists("icon.ico"):
        cmd = [c for c in cmd if not c.startswith("--icon")]
    
    # Nếu không có README, bỏ option --add-data
    if not os.path.exists("README.md"):
        cmd = [c for c in cmd if not c.startswith("--add-data")]
    
    try:
        print("📦 Đang build executable...")
        print(f"Command: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Build thành công!")
            
            # Kiểm tra file exe được tạo
            exe_path = os.path.join("dist", "KlingRegisterTool.exe")
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 File exe: {exe_path}")
                print(f"📏 Kích thước: {file_size:.1f} MB")
                print(f"🎉 Hoàn thành! Bạn có thể chạy file: {exe_path}")
                return True
            else:
                print("❌ Không tìm thấy file exe sau khi build")
                return False
        else:
            print("❌ Lỗi build:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("❌ Không tìm thấy PyInstaller. Vui lòng cài đặt: pip install pyinstaller")
        return False
    except Exception as e:
        print(f"❌ Lỗi: {str(e)}")
        return False

def create_spec_file():
    """Tạo file .spec để custom build"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['kling_register_gui.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui', 
        'PyQt5.QtWidgets',
        'requests',
        'json',
        'threading',
        'concurrent.futures',
        'urllib.parse',
        'random',
        'string',
        'time',
        'datetime',
        're'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='KlingRegisterTool',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='icon.ico'
)
'''
    
    with open("KlingRegisterTool.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ Đã tạo file KlingRegisterTool.spec")

def main():
    """Main function"""
    print("🎬 Kling AI Register Tool - Build Script")
    print("=" * 50)
    
    # Bước 1: Cài đặt PyInstaller
    if not install_pyinstaller():
        return
    
    # Bước 2: Tạo spec file
    create_spec_file()
    
    # Bước 3: Build executable
    if build_executable():
        print("\n🎉 BUILD THÀNH CÔNG!")
        print("📁 File exe được tạo trong thư mục 'dist/'")
        print("🚀 Bạn có thể chạy file KlingRegisterTool.exe")
    else:
        print("\n❌ BUILD THẤT BẠI!")
        print("💡 Hãy kiểm tra lỗi ở trên và thử lại")

if __name__ == "__main__":
    main()

# 🚀 Hướng dẫn Build File .EXE

## Phương pháp 1: Sử dụng Batch File (Đơn giản nhất)

1. **Chạy file batch:**
   ```
   build.bat
   ```
   
2. **Đợi quá trình hoàn thành**
   - Script sẽ tự động cài đặt PyInstaller
   - Xóa các file build cũ
   - Build file .exe mới

3. **Kết quả:**
   - File .exe sẽ được tạo trong thư mục `dist/`
   - Tên file: `KlingRegisterTool.exe`

## Phương pháp 2: Sử dụng Python Script

1. **Chạy build script:**
   ```
   python build_exe.py
   ```

2. **Script sẽ tự động:**
   - Kiểm tra và cài đặt PyInstaller
   - Tạo file .spec
   - Build executable

## Phương pháp 3: Manual với PyInstaller

1. **Cài đặt PyInstaller:**
   ```
   pip install pyinstaller
   ```

2. **Build command cơ bản:**
   ```
   pyinstaller --onefile --windowed kling_register_gui.py
   ```

3. **Build command đầy đủ:**
   ```
   pyinstaller --onefile --windowed --name=KlingRegisterTool --hidden-import=PyQt5.QtCore --hidden-import=PyQt5.QtGui --hidden-import=PyQt5.QtWidgets --hidden-import=requests --hidden-import=json --hidden-import=threading --hidden-import=concurrent.futures --clean kling_register_gui.py
   ```

## Các tùy chọn PyInstaller

- `--onefile`: Tạo single executable file
- `--windowed`: Không hiện console window
- `--name=KlingRegisterTool`: Đặt tên file exe
- `--hidden-import`: Import các module cần thiết
- `--clean`: Xóa cache cũ trước khi build
- `--icon=icon.ico`: Thêm icon (nếu có file icon.ico)

## Kết quả

Sau khi build thành công:
- File .exe sẽ ở trong thư mục `dist/`
- Kích thước file khoảng 50-100MB
- File có thể chạy độc lập không cần Python

## Lưu ý

- Đảm bảo tất cả dependencies đã được cài đặt
- File .exe chỉ chạy được trên Windows
- Lần đầu build có thể mất 5-10 phút
- Nếu có lỗi, kiểm tra console output để debug

## Troubleshooting

**Lỗi "Module not found":**
- Thêm `--hidden-import=module_name` vào command

**File .exe quá lớn:**
- Sử dụng `--exclude-module` để loại bỏ modules không cần thiết

**Lỗi khi chạy .exe:**
- Kiểm tra antivirus có block file không
- Chạy với quyền administrator
